@page "/regattas/{id}/admin"
@rendermode InteractiveServer
@using Microsoft.AspNetCore.Authorization
@using ProScoring.BusinessLogic.ServiceInterfaces
@using ProScoring.Domain.Entities.RegattaEntities
@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor
@attribute [Authorize]
@inject IRegattaService RegattaService
@inject NavigationManager NavigationManager

<PageTitle>@(regatta?.Name ?? "Loading...") - Admin</PageTitle>

@if (regatta == null)
{
    <RadzenStack Gap="1rem">
        <RadzenProgressBarCircular ShowValue="false" Mode="ProgressBarMode.Indeterminate" Size="ProgressBarCircularSize.Large" />
        <RadzenText TextStyle="TextStyle.H6">Loading regatta...</RadzenText>
    </RadzenStack>
}
else
{
    <RadzenStack Gap="1rem">
        <RadzenBreadCrumb>
            <RadzenBreadCrumbItem Path="/" Text="Home" />
            <RadzenBreadCrumbItem Path="/regattas" Text="Regattas" />
            <RadzenBreadCrumbItem Text="@regatta.Name" />
            <RadzenBreadCrumbItem Text="Admin" />
        </RadzenBreadCrumb>

        <RadzenText TextStyle="TextStyle.H3" TagName="TagName.H1">@regatta.Name - Administration</RadzenText>

        <RadzenAlert AlertStyle="AlertStyle.Success" Variant="Variant.Flat" Shade="Shade.Lighter">
            <RadzenIcon Icon="check_circle" />
            <RadzenText TextStyle="TextStyle.Body1">
                Regatta created successfully! This is a placeholder admin page.
            </RadzenText>
        </RadzenAlert>

        <RadzenCard>
            <RadzenStack Gap="1rem">
                <RadzenText TextStyle="TextStyle.H5">Regatta Details</RadzenText>
                
                <RadzenRow>
                    <RadzenColumn Size="12" SizeMD="6">
                        <RadzenText TextStyle="TextStyle.Subtitle2">Name:</RadzenText>
                        <RadzenText TextStyle="TextStyle.Body1">@regatta.Name</RadzenText>
                    </RadzenColumn>
                    <RadzenColumn Size="12" SizeMD="6">
                        <RadzenText TextStyle="TextStyle.Subtitle2">Location:</RadzenText>
                        <RadzenText TextStyle="TextStyle.Body1">@regatta.Location</RadzenText>
                    </RadzenColumn>
                </RadzenRow>

                <RadzenRow>
                    <RadzenColumn Size="12" SizeMD="6">
                        <RadzenText TextStyle="TextStyle.Subtitle2">Start Date:</RadzenText>
                        <RadzenText TextStyle="TextStyle.Body1">@regatta.StartDate.ToString("MMMM dd, yyyy")</RadzenText>
                    </RadzenColumn>
                    <RadzenColumn Size="12" SizeMD="6">
                        <RadzenText TextStyle="TextStyle.Subtitle2">End Date:</RadzenText>
                        <RadzenText TextStyle="TextStyle.Body1">@regatta.EndDate.ToString("MMMM dd, yyyy")</RadzenText>
                    </RadzenColumn>
                </RadzenRow>

                @if (!string.IsNullOrEmpty(regatta.Description))
                {
                    <RadzenRow>
                        <RadzenColumn Size="12">
                            <RadzenText TextStyle="TextStyle.Subtitle2">Description:</RadzenText>
                            <RadzenText TextStyle="TextStyle.Body1">@regatta.Description</RadzenText>
                        </RadzenColumn>
                    </RadzenRow>
                }

                <RadzenRow>
                    <RadzenColumn Size="12" SizeMD="6">
                        <RadzenText TextStyle="TextStyle.Subtitle2">Entry Fee:</RadzenText>
                        <RadzenText TextStyle="TextStyle.Body1">@regatta.EntryFee.ToString("C")</RadzenText>
                    </RadzenColumn>
                    <RadzenColumn Size="12" SizeMD="6">
                        <RadzenText TextStyle="TextStyle.Subtitle2">Late Fee:</RadzenText>
                        <RadzenText TextStyle="TextStyle.Body1">@regatta.LateFee.ToString("C")</RadzenText>
                    </RadzenColumn>
                </RadzenRow>

                @if (regatta.LateFeeDate.HasValue)
                {
                    <RadzenRow>
                        <RadzenColumn Size="12" SizeMD="6">
                            <RadzenText TextStyle="TextStyle.Subtitle2">Late Fee Date:</RadzenText>
                            <RadzenText TextStyle="TextStyle.Body1">@regatta.LateFeeDate.Value.ToString("MMMM dd, yyyy")</RadzenText>
                        </RadzenColumn>
                    </RadzenRow>
                }

                @if (regatta.RegistrationCloseDate.HasValue)
                {
                    <RadzenRow>
                        <RadzenColumn Size="12" SizeMD="6">
                            <RadzenText TextStyle="TextStyle.Subtitle2">Registration Close Date:</RadzenText>
                            <RadzenText TextStyle="TextStyle.Body1">@regatta.RegistrationCloseDate.Value.ToString("MMMM dd, yyyy")</RadzenText>
                        </RadzenColumn>
                    </RadzenRow>
                }
            </RadzenStack>
        </RadzenCard>

        <RadzenStack Orientation="Orientation.Horizontal" Gap="1rem">
            <RadzenButton Variant="Variant.Outlined" Click="@(() => NavigationManager.NavigateTo("/"))">
                Back to Home
            </RadzenButton>
            <RadzenButton Variant="Variant.Filled" Disabled="true">
                Edit Regatta (Coming Soon)
            </RadzenButton>
        </RadzenStack>
    </RadzenStack>
}

@code {
    [Parameter] public string Id { get; set; } = string.Empty;
    
    private Regatta? regatta;

    protected override async Task OnInitializedAsync()
    {
        if (!string.IsNullOrEmpty(Id))
        {
            try
            {
                regatta = await RegattaService.GetByIdAsync(Id);
            }
            catch (Exception)
            {
                // Handle error - regatta not found or access denied
                NavigationManager.NavigateTo("/");
            }
        }
    }
}
