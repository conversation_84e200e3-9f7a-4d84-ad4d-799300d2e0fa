using ProScoring.Domain.Dtos;
using ProScoring.Domain.Entities.RegattaEntities;

namespace ProScoring.BusinessLogic.ServiceInterfaces;

/// <summary>
/// Service interface for managing regattas.
/// </summary>
public interface IRegattaService
{
    /// <summary>
    /// Creates a new regatta for the current user's organizing authority.
    /// </summary>
    /// <param name="dto">The regatta creation data.</param>
    /// <returns>The created regatta.</returns>
    /// <exception cref="UnauthorizedAccessException">Thrown when the user doesn't have permission to create regattas.</exception>
    /// <exception cref="ArgumentException">Thrown when the DTO is invalid.</exception>
    Task<Regatta> CreateAsync(RegattaCreateDto dto);

    /// <summary>
    /// Gets a regatta by its ID.
    /// </summary>
    /// <param name="id">The regatta ID.</param>
    /// <returns>The regatta if found and accessible, otherwise null.</returns>
    Task<Regatta?> GetByIdAsync(string id);

    /// <summary>
    /// Validates a regatta creation DTO.
    /// </summary>
    /// <param name="dto">The DTO to validate.</param>
    /// <returns>A list of validation errors, empty if valid.</returns>
    Task<List<string>> ValidateAsync(RegattaCreateDto dto);
}
