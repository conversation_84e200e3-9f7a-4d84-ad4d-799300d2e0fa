{
  "ConnectionStrings": {
    "SQLiteConnection": "Data Source=../../proscoring.db",
    "PostgreSqlConnection": "use aspire provided connection string"
  },
  "Database": {
    "UseSqLite": "True"
  },
  "EmailSender": {
    "Enabled": "true",
    "ApiKey": "look in secrets",
    "FromAddress": "<EMAIL>"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "FileUpload": {
    "MaxSize": 1048576, // 1MB
    "MaxBurgeeSize": 1048576, // 1MB
    "MaxRegattaLogoSize": 1048576, // 1MB
    "MaxDocumentSize": 10485760, // 10MB
    "MaxImageSize": 5242880, // 5MB
    "UploadFilesPath": "uploads"
  },
  "LocalStorage": {
    "ExpirationTimeoutMinutes": 1440 // 24 hours
  },
  "AllowedHosts": "*"
}