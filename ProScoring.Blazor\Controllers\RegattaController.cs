using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OData.Routing.Controllers;
using ProScoring.BusinessLogic.ServiceInterfaces;
using ProScoring.Domain.Dtos;
using ProScoring.Infrastructure.Authorization;

namespace ProScoring.Blazor.Controllers;

/// <summary>
/// Controller for managing regattas in the ProScoring system.
/// Provides endpoints for CRUD operations and regatta management.
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class RegattaController : ODataController
{
    #region Fields

    private readonly ILogger<RegattaController> _logger;
    private readonly IRegattaService _service;

    #endregion Fields

    #region Constructor

    /// <summary>
    /// Initializes a new instance of the <see cref="RegattaController"/> class.
    /// </summary>
    /// <param name="service">The regatta service.</param>
    /// <param name="logger">The logger.</param>
    public RegattaController(IRegattaService service, ILogger<RegattaController> logger)
    {
        _service = service;
        _logger = logger;
    }

    #endregion Constructor

    #region API Endpoints

    /// <summary>
    /// Creates a new regatta for the current user's organizing authority.
    /// </summary>
    /// <param name="dto">The regatta creation data.</param>
    /// <returns>201 Created with the new regatta if successful.</returns>
    /// <response code="201">Returns the newly created regatta.</response>
    /// <response code="400">If the dto is null or invalid.</response>
    /// <response code="403">If the user lacks permission to create regattas.</response>
    [HttpPost]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> Create([FromBody] RegattaCreateDto? dto)
    {
        if (dto == null)
        {
            return BadRequest("Regatta data is required");
        }

        try
        {
            // Validate the DTO first
            var validationErrors = await _service.ValidateAsync(dto);
            if (validationErrors.Any())
            {
                return BadRequest(new { message = "Validation failed", errors = validationErrors });
            }

            // Create the regatta
            var regatta = await _service.CreateAsync(dto);

            _logger.LogInformation(
                "Successfully created regatta {RegattaName} with ID {RegattaId}",
                regatta.Name,
                regatta.Id
            );

            // Return 201 Created with Location header
            return CreatedAtAction(nameof(GetById), new { id = regatta.Id }, regatta);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning("Unauthorized attempt to create regatta: {Message}", ex.Message);
            return Forbid();
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning("Invalid regatta data: {Message}", ex.Message);
            return BadRequest(new { message = ex.Message });
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning("Invalid operation: {Message}", ex.Message);
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create regatta");
            return StatusCode(500, new { message = "An error occurred while creating the regatta" });
        }
    }

    /// <summary>
    /// Gets a regatta by its ID.
    /// </summary>
    /// <param name="id">The regatta ID.</param>
    /// <returns>The regatta if found and accessible.</returns>
    /// <response code="200">Returns the regatta.</response>
    /// <response code="404">If the regatta is not found or not accessible.</response>
    [HttpGet("{id}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetById(string id)
    {
        try
        {
            var regatta = await _service.GetByIdAsync(id);
            if (regatta == null)
            {
                return NotFound();
            }

            return Ok(regatta);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get regatta {RegattaId}", id);
            return StatusCode(500, new { message = "An error occurred while retrieving the regatta" });
        }
    }

    #endregion API Endpoints
}
